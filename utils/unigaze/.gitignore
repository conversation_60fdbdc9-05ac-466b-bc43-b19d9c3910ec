.DS_Store
*.h5
*.json
.vscode
*.pth
result/
*.npy
*.pdf
cache/
*.txt
!requirements.txt
*.drawio

*.ipynb
!load_gaze_model.ipynb
.ipynb_checkpoints
*.npy
*.dat
*.bz2

figures/
*.csv
prototype/
__pycache__/
results/
*.xlsx
run_on_server/

memo/
ckpt/



sig.sh
*.pkl

*.pt
*.pth

*.pyc

# src/clip
# src/taming-transformers

*.ckpt
*.pth.tar
checkpoints/*

!checkpoints/pre_trained_ckpt.md
!checkpoints/*.md

logs/*

*.jpg
*.png
*.JPG

*.PNG
*.gif
__pycache__/

*.log
*.sh

configs/data_path.yaml

!assets/*.jpg
!configs/trainer/figs/*.jpg
!configs/trainer/figs/*.png
!assets/figs/*/*
!data/face_model.txt
batch_log*/

scripts/
*out
*_nodeinfo

outputs/