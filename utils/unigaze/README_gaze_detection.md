# Gaze Detection on Images

This module provides a comprehensive solution for detecting gaze direction in single images. It includes face detection, head pose estimation, image normalization, gaze prediction using trained models, and visualization of results.

## Features

- **Face Detection**: Automatic detection of faces and extraction of 68 facial landmarks
- **Head Pose Estimation**: Estimation of head rotation and translation using PnP algorithm
- **Camera Parameters**: Support for both custom camera parameters and automatic dummy camera model
- **Image Normalization**: Standardization of face images for consistent model input
- **Gaze Prediction**: Deep learning-based gaze direction prediction
- **3D Gaze Visualization**: Realistic gaze arrows with shadow and gradient effects
- **Batch Processing**: Efficient processing of multiple images
- **Flexible Output**: Save both original images with gaze overlay and normalized face images

## Files

- `predict_gaze_image.py`: Main module containing the `GazeDetector` class
- `example_gaze_detection.py`: Example scripts showing different usage patterns
- `README_gaze_detection.md`: This documentation file

## Installation

Make sure you have all the required dependencies installed:

```bash
pip install torch torchvision opencv-python face-alignment omegaconf numpy
```

## Quick Start

### Command Line Usage

```bash
python predict_gaze_image.py \
    -i path/to/your/image.jpg \
    -m configs/model/unigaze_h14.yaml \
    --ckpt_resume logs/unigaze_h14_joint.pth.tar \
    -o output_directory \
    --save_normalized \
    --save_original
```

### Python API Usage

```python
from predict_gaze_image import GazeDetector

# Initialize detector
detector = GazeDetector(
    model_cfg_path="configs/model/unigaze_h14.yaml",
    checkpoint_path="logs/unigaze_h14_joint.pth.tar",
    device="auto"
)

# Process an image
results = detector.process_image(
    image_path="path/to/image.jpg",
    output_dir="output",
    save_normalized=True,
    save_original=True
)

# Access results
print(f"Detected {results['faces_detected']} faces")
for i, face_result in enumerate(results['results']):
    gaze = face_result['gaze_pitch_yaw']
    print(f"Face {i}: Gaze (pitch={gaze[0]:.3f}, yaw={gaze[1]:.3f}) radians")
```

## API Reference

### GazeDetector Class

#### Constructor

```python
GazeDetector(model_cfg_path, checkpoint_path, device='auto')
```

- `model_cfg_path`: Path to model configuration YAML file
- `checkpoint_path`: Path to trained model checkpoint (.pth.tar file)
- `device`: Computation device ('auto', 'cuda', 'cpu')

#### Main Methods

##### process_image()

```python
process_image(image_path, camera_matrix=None, camera_distortion=None, 
              output_dir=None, save_normalized=False, save_original=True)
```

Process a single image for gaze detection.

**Parameters:**
- `image_path`: Path to input image
- `camera_matrix`: 3x3 camera intrinsic matrix (optional)
- `camera_distortion`: Camera distortion coefficients (optional)
- `output_dir`: Directory to save results (optional)
- `save_normalized`: Whether to save normalized face images
- `save_original`: Whether to save original image with gaze overlay

**Returns:**
Dictionary containing:
- `faces_detected`: Number of faces found
- `results`: List of face analysis results
- `image_with_gaze`: Original image with gaze arrows drawn
- `original_image`: Unmodified original image

##### set_camera_matrix()

```python
set_camera_matrix(camera_matrix=None, camera_distortion=None, image_shape=None)
```

Set camera parameters. If not provided, creates a dummy camera model.

#### Individual Processing Methods

- `detect_faces_and_landmarks(image)`: Detect faces and extract landmarks
- `estimate_head_pose(landmarks, camera_matrix, camera_distortion)`: Estimate head pose
- `normalize_face_image(image, landmarks, hr, ht, camera_matrix)`: Normalize face image
- `predict_gaze(normalized_image)`: Predict gaze direction
- `denormalize_gaze(gaze_pitch_yaw, R_inv)`: Convert gaze back to camera coordinates
- `project_gaze_to_image(face_center, gaze_3d, camera_matrix, ...)`: Project 3D gaze to 2D
- `draw_gaze_on_normalized_image(image, pitch_yaw, ...)`: Draw gaze on normalized image
- `draw_gaze_on_original_image(image, start_point, end_point, ...)`: Draw gaze on original image

## Result Structure

Each face detection result contains:

```python
{
    "face_id": int,                    # Face identifier
    "landmarks": np.ndarray,           # 68 facial landmarks
    "head_pose": {
        "rotation": np.ndarray,        # Head rotation vector
        "translation": np.ndarray      # Head translation vector
    },
    "gaze_pitch_yaw": np.ndarray,      # Predicted gaze (pitch, yaw) in radians
    "gaze_3d": np.ndarray,             # 3D gaze direction vector
    "gaze_denormalized": np.ndarray,   # Gaze in camera coordinates
    "face_center": np.ndarray,         # 3D face center position
    "bbox": tuple,                     # Face bounding box (x_min, y_min, x_max, y_max)
    "gaze_points": {
        "start": tuple,                # Gaze line start point in image
        "end": tuple                   # Gaze line end point in image
    },
    "normalized_image": np.ndarray     # Normalized face image with gaze arrow
}
```

## Camera Parameters

### Using Custom Camera Parameters

If you have camera calibration data:

```python
import numpy as np

# Camera intrinsic matrix
camera_matrix = np.array([
    [fx, 0, cx],
    [0, fy, cy],
    [0, 0, 1]
], dtype=np.float64)

# Distortion coefficients [k1, k2, p1, p2, k3]
camera_distortion = np.array([k1, k2, p1, p2, k3])

# Use with detector
results = detector.process_image(
    image_path="image.jpg",
    camera_matrix=camera_matrix,
    camera_distortion=camera_distortion
)
```

### Automatic Camera Model

If camera parameters are not available, the system automatically creates a dummy camera model based on image dimensions.

## Examples

See `example_gaze_detection.py` for comprehensive examples including:

1. **Basic Usage**: Simple gaze detection on a single image
2. **Custom Camera Parameters**: Using known camera calibration
3. **Batch Processing**: Processing multiple images efficiently
4. **Detailed Analysis**: Extracting comprehensive gaze information
5. **Real-time Processing**: Framework for webcam-based detection

## Model Requirements

You need:
1. A trained UniGaze model checkpoint (`.pth.tar` file)
2. Corresponding model configuration file (`.yaml` file)
3. Face model file (`data/face_model.txt`)

## Output Files

When `output_dir` is specified:
- `{image_name}_gaze.jpg`: Original image with gaze arrows and face boxes
- `{image_name}_face_{id}_normalized.jpg`: Normalized face images (if `save_normalized=True`)

## Performance Notes

- GPU acceleration is automatically used when available
- Face detection is the most time-consuming step
- Model inference is fast once faces are detected and normalized
- For real-time applications, consider reducing image resolution

## Troubleshooting

**No faces detected:**
- Ensure faces are clearly visible and well-lit
- Try different image resolutions
- Check if faces are too small or too large in the image

**Poor gaze accuracy:**
- Ensure proper camera calibration if using custom parameters
- Check that head pose is not too extreme (>80° rotation)
- Verify model checkpoint is compatible with configuration

**Memory issues:**
- Use CPU device for large images: `device="cpu"`
- Process images in smaller batches
- Reduce image resolution before processing
