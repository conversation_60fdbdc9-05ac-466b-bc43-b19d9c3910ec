"""
Gaze Detection on Images

This module provides functionality to detect gaze direction on single images.
It includes face detection, camera matrix estimation, head pose estimation,
landmark detection, gaze prediction, and gaze visualization.

Usage:
    python predict_gaze_image.py -i input_image.jpg -m model_config.yaml --ckpt_resume model.pth -o output_dir
"""

import argparse
import os
import numpy as np
import cv2
import torch
import torch.nn.functional as F
from omegaconf import OmegaConf
import face_alignment
from datetime import datetime

# Import UniGaze modules
from utils import instantiate_from_cfg
from datasets.helper.image_transform import wrap_transforms
from gazelib.gaze.gaze_utils import pitchyaw_to_vector, vector_to_pitchyaw
from gazelib.gaze.normalize import estimateHeadPose, normalize
from gazelib.label_transform import get_face_center_by_nose


class GazeDetector:
    """
    A class for detecting gaze direction in images.
    
    This class encapsulates all the functionality needed for gaze detection:
    - Face detection and landmark extraction
    - Head pose estimation
    - Image normalization
    - Gaze prediction using trained models
    - Gaze visualization
    """
    
    def __init__(self, model_cfg_path, checkpoint_path, device='auto'):
        """
        Initialize the GazeDetector.
        
        Args:
            model_cfg_path (str): Path to model configuration file
            checkpoint_path (str): Path to trained model checkpoint
            device (str): Device to run inference on ('auto', 'cuda', 'cpu')
        """
        self.device = self._setup_device(device)
        self.model = self._load_model(model_cfg_path, checkpoint_path)
        self.face_detector = self._setup_face_detector()
        self.image_transform = wrap_transforms('basic_imagenet', image_size=224)
        
        # Normalization parameters
        self.focal_norm = 960  # focal length of normalized camera
        self.distance_norm = 600  # normalized distance between eye and camera
        self.roi_size = (224, 224)  # size of cropped eye image
        
        # Load face model for head pose estimation
        self.face_model_load = np.loadtxt('data/face_model.txt')
        self.face_model = self.face_model_load[[20, 23, 26, 29, 15, 19], :]
        self.face_pts = self.face_model.reshape(6, 1, 3)
        
    def _setup_device(self, device):
        """Setup computation device."""
        if device == 'auto':
            return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.device(device)
    
    def _load_model(self, model_cfg_path, checkpoint_path):
        """Load the gaze prediction model."""
        # Load model configuration
        model_cfg = OmegaConf.load(model_cfg_path)['net_config']
        model_cfg.params.custom_pretrained_path = None
        
        # Instantiate model
        model = instantiate_from_cfg(model_cfg)
        
        # Load checkpoint
        self._load_checkpoint(model, 'model_state', checkpoint_path)
        
        model.eval()
        model.to(self.device)
        return model
    
    def _load_checkpoint(self, model, ckpt_key, ckpt_path):
        """Load model weights from checkpoint."""
        assert os.path.isfile(ckpt_path), f"Checkpoint not found: {ckpt_path}"
        
        weights = torch.load(ckpt_path, map_location='cpu')
        print(f'Loaded checkpoint from: {ckpt_path}')
        
        # Handle DataParallel models
        model_state = weights[ckpt_key]
        if next(iter(model_state.keys())).startswith('module.'):
            print('Converting DataParallel state to normal state')
            model_state = dict([(k[7:], v) for k, v in model_state.items()])
        
        model.load_state_dict(model_state, strict=True)
        print(f'Loaded {ckpt_key}')
        del weights
    
    def _setup_face_detector(self):
        """Setup face alignment detector."""
        try:
            return face_alignment.FaceAlignment(face_alignment.LandmarksType.TWO_D, flip_input=False)
        except:
            return face_alignment.FaceAlignment(face_alignment.LandmarksType._2D, flip_input=False)
    
    def set_camera_matrix(self, camera_matrix=None, camera_distortion=None, image_shape=None):
        """
        Set camera matrix and distortion parameters.
        
        Args:
            camera_matrix (np.ndarray): 3x3 camera intrinsic matrix
            camera_distortion (np.ndarray): Camera distortion coefficients
            image_shape (tuple): Image shape (height, width) for dummy camera model
        """
        if camera_matrix is not None:
            self.camera_matrix = camera_matrix
            self.camera_distortion = camera_distortion if camera_distortion is not None else np.zeros((1, 5))
        else:
            # Use dummy camera model
            self.camera_matrix, self.camera_distortion = self._set_dummy_camera_model(image_shape)
    
    def _set_dummy_camera_model(self, image_shape):
        """Create a dummy camera model when real camera parameters are not available."""
        if image_shape is None:
            raise ValueError("Image shape must be provided for dummy camera model")
        
        h, w = image_shape[:2]
        focal_length = w * 4
        center = (w // 2, h // 2)
        camera_matrix = np.array([
            [focal_length, 0, center[0]],
            [0, focal_length, center[1]],
            [0, 0, 1]
        ], dtype="double")
        camera_distortion = np.zeros((1, 5))  # Assuming no lens distortion
        return camera_matrix, camera_distortion

    
    def detect_faces_and_landmarks(self, image):
        """
        Detect faces and extract facial landmarks.

        Args:
            image (np.ndarray): Input image in BGR format

        Returns:
            list: List of detected landmarks for each face
        """
        # Convert BGR to RGB for face_alignment
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        landmarks = self.face_detector.get_landmarks(image_rgb)
        return landmarks if landmarks is not None else []

    def estimate_head_pose(self, landmarks, camera_matrix, camera_distortion):
        """
        Estimate head pose from facial landmarks.

        Args:
            landmarks (np.ndarray): Facial landmarks (68 points)
            camera_matrix (np.ndarray): Camera intrinsic matrix
            camera_distortion (np.ndarray): Camera distortion coefficients

        Returns:
            tuple: (rotation_vector, translation_vector)
        """
        # Select specific landmarks for head pose estimation
        landmarks_sub = landmarks[[36, 39, 42, 45, 31, 35], :]
        landmarks_sub = landmarks_sub.astype(float)
        landmarks_sub = landmarks_sub.reshape(6, 1, 2)

        # Estimate head pose using PnP
        hr, ht = estimateHeadPose(landmarks_sub, self.face_pts, camera_matrix, camera_distortion)
        return hr, ht

    def normalize_face_image(self, image, landmarks, hr, ht, camera_matrix):
        """
        Normalize face image for gaze prediction.

        Args:
            image (np.ndarray): Face image
            landmarks (np.ndarray): Facial landmarks
            hr (np.ndarray): Head rotation vector
            ht (np.ndarray): Head translation vector
            camera_matrix (np.ndarray): Camera matrix

        Returns:
            tuple: (normalized_image, transformation_matrix, normalized_head_rotation)
        """
        # Get face center
        hR = cv2.Rodrigues(hr)[0]  # rotation matrix
        face_center_camera_cord, Fc_nose = get_face_center_by_nose(
            hR=hR, ht=ht, face_model_load=self.face_model_load
        )

        # Normalize image
        img_normalized, R, hR_norm, gaze_normalized, landmarks_normalized, _ = normalize(
            image, landmarks, self.focal_norm, self.distance_norm, self.roi_size,
            face_center_camera_cord, hr, ht, camera_matrix, gc=None
        )

        return img_normalized, R, hR_norm, face_center_camera_cord

    def predict_gaze(self, normalized_image):
        """
        Predict gaze direction from normalized face image.

        Args:
            normalized_image (np.ndarray): Normalized face image

        Returns:
            np.ndarray: Predicted gaze direction (pitch, yaw)
        """
        # Prepare input for model
        input_var = normalized_image[:, :, [2, 1, 0]]  # BGR to RGB
        input_var = self.image_transform(input_var)
        input_var = torch.autograd.Variable(input_var.float().to(self.device))
        input_var = input_var.unsqueeze(0)

        # Get prediction
        with torch.no_grad():
            ret = self.model(input_var)
            pred_gaze = ret["pred_gaze"][0]
            pred_gaze_np = pred_gaze.cpu().data.numpy()

        return pred_gaze_np

    def denormalize_gaze(self, gaze_pitch_yaw, R_inv):
        """
        Convert normalized gaze back to camera coordinate system.

        Args:
            gaze_pitch_yaw (np.ndarray): Predicted gaze in pitch-yaw format
            R_inv (np.ndarray): Inverse transformation matrix

        Returns:
            tuple: (3D_gaze_vector, pitch_yaw_in_camera_coords)
        """
        # Convert to 3D vector
        pred_gaze_3d = pitchyaw_to_vector(gaze_pitch_yaw.reshape(1, 2)).reshape(3, 1)

        # Apply inverse transformation
        pred_gaze_3d = np.matmul(R_inv, pred_gaze_3d.reshape(3, 1))
        pred_gaze_3d = pred_gaze_3d / np.linalg.norm(pred_gaze_3d)  # normalize

        # Convert back to pitch-yaw
        pred_pitch_yaw = vector_to_pitchyaw(pred_gaze_3d.reshape(1, 3))

        return pred_gaze_3d, pred_pitch_yaw


    def project_gaze_to_image(self, face_center, gaze_3d, camera_matrix, camera_distortion,
                             offset_x=0, offset_y=0, scale_factor=1.5):
        """
        Project 3D gaze vector back to 2D image coordinates.

        Args:
            face_center (np.ndarray): 3D face center position
            gaze_3d (np.ndarray): 3D gaze direction vector
            camera_matrix (np.ndarray): Camera matrix
            camera_distortion (np.ndarray): Camera distortion
            offset_x, offset_y (int): Offset for cropped face region
            scale_factor (float): Scale factor for gaze vector length

        Returns:
            tuple: (start_point, end_point) in image coordinates
        """
        # Create gaze ray
        vec_length = gaze_3d * -112 * scale_factor
        gaze_ray = np.concatenate((
            face_center.reshape(1, 3),
            (face_center + vec_length).reshape(1, 3)
        ), axis=0)

        # Project to image coordinates
        result = cv2.projectPoints(
            gaze_ray,
            np.array([0, 0, 0]).reshape(3, 1).astype(float),
            np.array([0, 0, 0]).reshape(3, 1).astype(float),
            camera_matrix, camera_distortion
        )
        result = result[0].reshape(2, 2)

        # Add offset for cropped region
        result += np.array([offset_x, offset_y])

        start_point = (int(result[0][0]), int(result[0][1]))
        end_point = (int(result[1][0]), int(result[1][1]))

        return start_point, end_point

    def draw_gaze_on_normalized_image(self, image, pitch_yaw, thickness=5, color=(0, 0, 255)):
        """
        Draw gaze vector on normalized face image.

        Args:
            image (np.ndarray): Normalized face image
            pitch_yaw (np.ndarray): Gaze direction in pitch-yaw format
            thickness (int): Arrow thickness
            color (tuple): Arrow color (B, G, R)

        Returns:
            np.ndarray: Image with gaze arrow drawn
        """
        image_out = image.copy()
        (h, w) = image.shape[:2]
        length = w / 2.0
        pos = (int(h / 2.0), int(w / 2.0))

        # Convert grayscale to BGR if needed
        if len(image_out.shape) == 2 or image_out.shape[2] == 1:
            image_out = cv2.cvtColor(image_out, cv2.COLOR_GRAY2BGR)

        # Calculate gaze direction end point
        dx = -length * np.sin(pitch_yaw[1]) * np.cos(pitch_yaw[0])
        dy = -length * np.sin(pitch_yaw[0])
        end_point = (int(pos[0] + dx), int(pos[1] + dy))

        # Add shadow effect for 3D look
        shadow_offset = 2
        shadow_color = (40, 40, 40)
        shadow_end = (end_point[0] + shadow_offset, end_point[1] + shadow_offset)
        cv2.arrowedLine(image_out,
                       (pos[0] + shadow_offset, pos[1] + shadow_offset),
                       shadow_end, shadow_color, thickness + 2,
                       cv2.LINE_AA, tipLength=0.3)

        # Draw main arrow with gradient layers
        thickness_values = [4, 3, 2, 1]
        num_layers = len(thickness_values)
        for i in range(num_layers):
            alpha = i / num_layers
            layer_color = tuple(int((1 - alpha) * color[j] + alpha * 255) for j in range(3))
            cv2.arrowedLine(image_out, pos, end_point, layer_color,
                           thickness_values[i], cv2.LINE_AA, tipLength=0.3)

        return image_out

    def draw_gaze_on_original_image(self, image, start_point, end_point,
                                   bbox=None, color=(47, 255, 173)):
        """
        Draw gaze vector on original image.

        Args:
            image (np.ndarray): Original image
            start_point (tuple): Gaze vector start point
            end_point (tuple): Gaze vector end point
            bbox (tuple): Face bounding box (x_min, y_min, x_max, y_max)
            color (tuple): Arrow color (B, G, R)

        Returns:
            np.ndarray: Image with gaze arrow and face box drawn
        """
        image_out = image.copy()

        # Draw face bounding box if provided
        if bbox is not None:
            x_min, y_min, x_max, y_max = bbox
            cv2.rectangle(image_out, (x_min, y_min), (x_max, y_max), (0, 0, 240), 2)

        # Add shadow effect
        shadow_offset = 2
        shadow_color = (40, 40, 40)
        shadow_end = (end_point[0] + shadow_offset, end_point[1] + shadow_offset)
        cv2.arrowedLine(image_out,
                       (start_point[0] + shadow_offset, start_point[1] + shadow_offset),
                       shadow_end, shadow_color, 5, cv2.LINE_AA, tipLength=0.2)

        # Draw main arrow with gradient layers
        thickness_values = [x * 3 for x in [4, 3, 2, 1]]
        num_layers = len(thickness_values)
        for i in range(num_layers):
            alpha = i / num_layers
            layer_color = tuple(int((1 - alpha) * color[j] + alpha * 255) for j in range(3))
            cv2.arrowedLine(image_out, start_point, end_point, layer_color,
                           thickness_values[i], cv2.LINE_AA, tipLength=0.2)

        return image_out


    def draw_gaze_on_original_image(self, image, start_point, end_point,
                                   bbox=None, color=(47, 255, 173)):
        """
        Draw gaze vector on original image.

        Args:
            image (np.ndarray): Original image
            start_point (tuple): Gaze vector start point
            end_point (tuple): Gaze vector end point
            bbox (tuple): Face bounding box (x_min, y_min, x_max, y_max)
            color (tuple): Arrow color (B, G, R)

        Returns:
            np.ndarray: Image with gaze arrow and face box drawn
        """
        image_out = image.copy()

        # Draw face bounding box if provided
        if bbox is not None:
            x_min, y_min, x_max, y_max = bbox
            cv2.rectangle(image_out, (x_min, y_min), (x_max, y_max), (0, 0, 240), 2)

        # Add shadow effect
        shadow_offset = 2
        shadow_color = (40, 40, 40)
        shadow_end = (end_point[0] + shadow_offset, end_point[1] + shadow_offset)
        cv2.arrowedLine(image_out,
                       (start_point[0] + shadow_offset, start_point[1] + shadow_offset),
                       shadow_end, shadow_color, 5, cv2.LINE_AA, tipLength=0.2)

        # Draw main arrow with gradient layers
        thickness_values = [x * 3 for x in [4, 3, 2, 1]]
        num_layers = len(thickness_values)
        for i in range(num_layers):
            alpha = i / num_layers
            layer_color = tuple(int((1 - alpha) * color[j] + alpha * 255) for j in range(3))
            cv2.arrowedLine(image_out, start_point, end_point, layer_color,
                           thickness_values[i], cv2.LINE_AA, tipLength=0.2)

        return image_out

    def process_image(self, image, camera_matrix=None, camera_distortion=None,
                     output_dir=None, save_normalized=False, save_original=True, hr=None, ht=None):
        """
        Process a single image for gaze detection.

        Args:
            image_path (str): Path to input image
            camera_matrix (np.ndarray): Camera intrinsic matrix (optional)
            camera_distortion (np.ndarray): Camera distortion coefficients (optional)
            output_dir (str): Directory to save results (optional)
            save_normalized (bool): Whether to save normalized face images
            save_original (bool): Whether to save original image with gaze overlay

        Returns:
            dict: Results containing gaze predictions and processed images
        """
        # Set camera parameters
        self.set_camera_matrix(camera_matrix, camera_distortion, image.shape)

        # Detect faces and landmarks
        landmarks_list = self.detect_faces_and_landmarks(image)

        if not landmarks_list:
            print("No faces detected in the image")
            return {"faces_detected": 0, "results": []}

        results = []
        image_with_gaze = image.copy()

        for face_idx, landmarks in enumerate(landmarks_list):
            try:
                # Calculate face bounding box
                x_min = int(landmarks[:, 0].min())
                x_max = int(landmarks[:, 0].max())
                y_min = int(landmarks[:, 1].min())
                y_max = int(landmarks[:, 1].max())

                # Expand bounding box
                scale_factor = 2.0
                bbox_width = x_max - x_min
                bbox_height = y_max - y_min
                bbox_center = ((x_min + x_max) // 2, (y_min + y_max) // 2)

                x_min_crop = max(0, bbox_center[0] - int(bbox_width * scale_factor // 2))
                x_max_crop = min(image.shape[1], bbox_center[0] + int(bbox_width * scale_factor // 2))
                y_min_crop = max(0, bbox_center[1] - int(bbox_height * scale_factor // 2))
                y_max_crop = min(image.shape[0], bbox_center[1] + int(bbox_height * scale_factor // 2))

                # Crop face region
                face_image = image[y_min_crop:y_max_crop, x_min_crop:x_max_crop]
                face_landmarks = landmarks - np.array([x_min_crop, y_min_crop])

                if hr is None or ht is None:
                    hr, ht = self.estimate_head_pose(face_landmarks, self.camera_matrix, self.camera_distortion)

                # Normalize face image
                img_normalized, R, hR_norm, face_center = self.normalize_face_image(
                    face_image, face_landmarks, hr, ht, self.camera_matrix
                )

                # Check head pose validity
                hr_norm = np.array([np.arcsin(hR_norm[1, 2]), np.arctan2(hR_norm[0, 2], hR_norm[2, 2])])
                if np.linalg.norm(hr_norm) > 80 * np.pi / 180:
                    print(f"Face {face_idx}: Head pose too extreme, skipping")
                    continue

                # Predict gaze
                pred_gaze = self.predict_gaze(img_normalized)

                # Draw gaze on normalized image
                img_normalized_with_gaze = self.draw_gaze_on_normalized_image(
                    img_normalized, pred_gaze, color=(47, 255, 173)
                )

                # Denormalize gaze
                R_inv = np.linalg.inv(R)
                gaze_3d, gaze_pitch_yaw = self.denormalize_gaze(pred_gaze, R_inv)

                # Project gaze to original image
                start_point, end_point = self.project_gaze_to_image(
                    face_center, gaze_3d, self.camera_matrix, self.camera_distortion,
                    x_min_crop, y_min_crop
                )

                # Draw bounding box for visualization
                bbox_draw = (
                    max(0, bbox_center[0] - int(bbox_width * 1.2 // 2)),
                    max(0, bbox_center[1] - int(bbox_height * 1.2 // 2)),
                    min(image.shape[1], bbox_center[0] + int(bbox_width * 1.2 // 2)),
                    min(image.shape[0], bbox_center[1] + int(bbox_height * 1.2 // 2))
                )

                # Draw gaze on original image
                image_with_gaze = self.draw_gaze_on_original_image(
                    image_with_gaze, start_point, end_point, bbox_draw
                )

                # Store results
                face_result = {
                    "face_id": face_idx,
                    "landmarks": landmarks,
                    "head_pose": {"rotation": hr, "translation": ht},
                    "gaze_pitch_yaw": pred_gaze,
                    "gaze_3d": gaze_3d,
                    "gaze_denormalized": gaze_pitch_yaw,
                    "face_center": face_center,
                    "bbox": bbox_draw,
                    "gaze_points": {"start": start_point, "end": end_point},
                    "normalized_image": img_normalized_with_gaze
                }
                results.append(face_result)

            except Exception as e:
                print(f"Error processing face {face_idx}: {str(e)}")
                continue

        # Save results if output directory is specified
        if output_dir and results:
            self._save_results(image_with_gaze, results, output_dir,
                             save_normalized, save_original)

        results = {
            "faces_detected": len(results),
            "results": results,
            "image_with_gaze": image_with_gaze,
            "original_image": image
        }
        print(f"\nResults:")
        print(f"Faces detected: {results['faces_detected']}")

        for i, face_result in enumerate(results['results']):
            gaze_pitch_yaw = face_result['gaze_pitch_yaw']
            print(f"Face {i}: Gaze direction (pitch, yaw) = ({gaze_pitch_yaw[0]:.3f}, {gaze_pitch_yaw[1]:.3f}) radians")

        if output_dir:
            print(f"\nResults saved to: {output_dir}")
            
        return gaze_pitch_yaw


    
    def _save_results(self,image_with_gaze, results, output_dir,
                     save_normalized, save_original):
        """Save processing results to files."""
        os.makedirs(output_dir, exist_ok=True)

        image_name = "output_image"

        # Save original image with gaze overlay
        if save_original:
            output_path = os.path.join(output_dir, f"{image_name}_gaze.jpg")
            cv2.imwrite(output_path, image_with_gaze)
            print(f"Saved result image: {output_path}")

        # Save normalized face images
        if save_normalized:
            for result in results:
                face_id = result["face_id"]
                normalized_img = result["normalized_image"]
                output_path = os.path.join(output_dir, f"{image_name}_face_{face_id}_normalized.jpg")
                cv2.imwrite(output_path, normalized_img)
                print(f"Saved normalized face image: {output_path}")


def get_parser():
    """Create argument parser for command line interface."""
    parser = argparse.ArgumentParser(description="Gaze Detection on Images")

    parser.add_argument(
        "-i", "--input", required=True,
        help="Path to input image"
    )

    parser.add_argument(
        "-m", "--model_cfg_path", required=True,
        help="Path to model configuration file"
    )

    parser.add_argument(
        "--ckpt_resume", required=True,
        help="Path to model checkpoint"
    )

    parser.add_argument(
        "-o", "--output_dir", default=None,
        help="Output directory to save results"
    )

    parser.add_argument(
        "--save_normalized", action="store_true",
        help="Save normalized face images"
    )

    parser.add_argument(
        "--save_original", action="store_true", default=True,
        help="Save original image with gaze overlay"
    )

    parser.add_argument(
        "--device", default="auto", choices=["auto", "cuda", "cpu"],
        help="Device to run inference on"
    )

    parser.add_argument(
        "--camera_matrix", default=None,
        help="Path to camera matrix file (optional)"
    )

    parser.add_argument(
        "--camera_distortion", default=None,
        help="Path to camera distortion file (optional)"
    )

    return parser


def load_camera_parameters(camera_matrix_path, camera_distortion_path):
    """Load camera parameters from files."""
    camera_matrix = None
    camera_distortion = None

    if camera_matrix_path and os.path.exists(camera_matrix_path):
        camera_matrix = np.loadtxt(camera_matrix_path)
        print(f"Loaded camera matrix from: {camera_matrix_path}")

    if camera_distortion_path and os.path.exists(camera_distortion_path):
        camera_distortion = np.loadtxt(camera_distortion_path)
        print(f"Loaded camera distortion from: {camera_distortion_path}")

    return camera_matrix, camera_distortion


def main():
    """Main function for command line interface."""
    parser = get_parser()
    args = parser.parse_args()

    # Check if input image exists
    if not os.path.exists(args.input):
        print(f"Error: Input image not found: {args.input}")
        return

    # Load camera parameters if provided
    camera_matrix, camera_distortion = load_camera_parameters(
        args.camera_matrix, args.camera_distortion
    )

    # Create output directory if specified
    if args.output_dir:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(args.output_dir, f"gaze_detection_{timestamp}")
    else:
        output_dir = None

    try:
        # Initialize gaze detector
        print("Initializing gaze detector...")
        detector = GazeDetector(
            model_cfg_path=args.model_cfg_path,
            checkpoint_path=args.ckpt_resume,
            device=args.device
        )

        # Process image
        print(f"Processing image: {args.input}")
        image = cv2.imread(args.input)
        if image is None:
            raise ValueError(f"Could not load image: {args.input}")
        results = detector.process_image(
            image=image,
            camera_matrix=camera_matrix,
            camera_distortion=camera_distortion,
            output_dir=output_dir,
            save_normalized=args.save_normalized,
            save_original=args.save_original
        )

    except Exception as e:
        print(f"Error: {str(e)}")
        return


if __name__ == "__main__":
    main()
