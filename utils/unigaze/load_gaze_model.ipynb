{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["\n", "import torch\n", "import numpy as np\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load pre-trained MAE"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded custom pretrained weights from checkpoints/mae_b16/mae_b16_checkpoint-299.pth\n", "Loaded custom pretrained weights from checkpoints/mae_l16/mae_l16_checkpoint-299.pth\n", "Loaded custom pretrained weights from checkpoints/mae_h14/mae_h14_checkpoint-299.pth\n"]}], "source": ["from models.vit.mae_gaze import MAE_Gaze\n", "\n", "mae_b16 = MAE_Gaze(model_type='vit_b_16', custom_pretrained_path='checkpoints/mae_b16/mae_b16_checkpoint-299.pth')\n", "mae_b16 = MAE_Gaze(model_type='vit_l_16', custom_pretrained_path='checkpoints/mae_l16/mae_l16_checkpoint-299.pth')\n", "mae_h14 = MAE_Gaze(model_type='vit_h_14', custom_pretrained_path='checkpoints/mae_h14/mae_h14_checkpoint-299.pth')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## load UniGaze-H trained with XGaze Train (the first 60 subjects) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["unigaze_h14_crossX = MAE_Gaze(model_type='vit_h_14')\n", "weight = torch.load('logs/unigaze_h14_cross_X.pth.tar', map_location='cpu')['model_state']\n", "unigaze_h14_crossX.load_state_dict(weight, strict=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## load UniGaze-B trained with *Joint-datasets*"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["unigaze_b16_joint = MAE_Gaze(model_type='vit_b_16')\n", "weight = torch.load('logs/unigaze_b16_joint.pth.tar', map_location='cpu')['model_state']\n", "unigaze_b16_joint.load_state_dict(weight, strict=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## load UniGaze-L trained with *Joint-datasets*"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["unigaze_l16_joint = MAE_Gaze(model_type='vit_l_16')\n", "weight = torch.load('logs/unigaze_l16_joint.pth.tar', map_location='cpu')['model_state']\n", "unigaze_l16_joint.load_state_dict(weight, strict=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## load UniGaze-H trained with *Joint-datasets*"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["unigaze_h14_joint = MAE_Gaze(model_type='vit_h_14')\n", "weight = torch.load('logs/unigaze_h14_joint.pth.tar', map_location='cpu')['model_state']\n", "unigaze_h14_joint.load_state_dict(weight, strict=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ldm_red", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 2}