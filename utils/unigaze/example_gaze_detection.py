"""
Example script demonstrating how to use the GazeDetector class for image-based gaze detection.

This script shows different ways to use the gaze detection functionality:
1. Basic usage with automatic camera parameters
2. Usage with custom camera parameters
3. Batch processing of multiple images
4. Extracting detailed gaze information
"""

import os
import numpy as np
import cv2
from predict_gaze_image import GazeDetector


def example_basic_usage():
    """Basic example of gaze detection on a single image."""
    print("=== Basic Gaze Detection Example ===")
    
    # Initialize the detector
    detector = GazeDetector(
        model_cfg_path="configs/model/unigaze_h14.yaml",  # Update with your model config
        checkpoint_path="logs/unigaze_h14_joint.pth.tar",  # Update with your checkpoint
        device="auto"
    )
    
    # Process an image
    image_path = "path/to/your/image.jpg"  # Update with your image path
    
    if os.path.exists(image_path):
        results = detector.process_image(
            image_path=image_path,
            output_dir="output/basic_example",
            save_normalized=True,
            save_original=True
        )
        
        print(f"Detected {results['faces_detected']} faces")
        
        for i, face_result in enumerate(results['results']):
            gaze = face_result['gaze_pitch_yaw']
            print(f"Face {i}: Gaze (pitch={gaze[0]:.3f}, yaw={gaze[1]:.3f}) radians")
    else:
        print(f"Image not found: {image_path}")


def example_with_camera_parameters():
    """Example using custom camera parameters."""
    print("\n=== Gaze Detection with Custom Camera Parameters ===")
    
    # Define camera parameters (example values)
    camera_matrix = np.array([
        [800, 0, 320],
        [0, 800, 240],
        [0, 0, 1]
    ], dtype=np.float64)
    
    camera_distortion = np.array([0.1, -0.2, 0.001, 0.002, 0.1])
    
    # Initialize detector
    detector = GazeDetector(
        model_cfg_path="configs/model/unigaze_h14.yaml",
        checkpoint_path="logs/unigaze_h14_joint.pth.tar",
        device="auto"
    )
    
    # Process image with custom camera parameters
    image_path = "path/to/your/image.jpg"
    
    if os.path.exists(image_path):
        results = detector.process_image(
            image_path=image_path,
            camera_matrix=camera_matrix,
            camera_distortion=camera_distortion,
            output_dir="output/camera_params_example"
        )
        
        print(f"Processed image with custom camera parameters")
        print(f"Detected {results['faces_detected']} faces")


def example_batch_processing():
    """Example of processing multiple images."""
    print("\n=== Batch Processing Example ===")
    
    # Initialize detector once for efficiency
    detector = GazeDetector(
        model_cfg_path="configs/model/unigaze_h14.yaml",
        checkpoint_path="logs/unigaze_h14_joint.pth.tar",
        device="auto"
    )
    
    # List of images to process
    image_paths = [
        "path/to/image1.jpg",
        "path/to/image2.jpg",
        "path/to/image3.jpg"
    ]
    
    all_results = []
    
    for i, image_path in enumerate(image_paths):
        if os.path.exists(image_path):
            print(f"Processing image {i+1}/{len(image_paths)}: {image_path}")
            
            results = detector.process_image(
                image_path=image_path,
                output_dir=f"output/batch_processing/image_{i+1}"
            )
            
            all_results.append({
                "image_path": image_path,
                "results": results
            })
        else:
            print(f"Image not found: {image_path}")
    
    # Summary
    total_faces = sum(r["results"]["faces_detected"] for r in all_results)
    print(f"\nBatch processing complete. Total faces detected: {total_faces}")


def example_detailed_analysis():
    """Example showing how to extract detailed gaze information."""
    print("\n=== Detailed Gaze Analysis Example ===")
    
    detector = GazeDetector(
        model_cfg_path="configs/model/unigaze_h14.yaml",
        checkpoint_path="logs/unigaze_h14_joint.pth.tar",
        device="auto"
    )
    
    image_path = "path/to/your/image.jpg"
    
    if os.path.exists(image_path):
        results = detector.process_image(image_path=image_path)
        
        for i, face_result in enumerate(results['results']):
            print(f"\n--- Face {i} Analysis ---")
            
            # Gaze direction
            gaze_pitch_yaw = face_result['gaze_pitch_yaw']
            gaze_3d = face_result['gaze_3d']
            
            print(f"Gaze direction (pitch, yaw): ({gaze_pitch_yaw[0]:.3f}, {gaze_pitch_yaw[1]:.3f}) radians")
            print(f"Gaze direction (degrees): ({np.degrees(gaze_pitch_yaw[0]):.1f}°, {np.degrees(gaze_pitch_yaw[1]):.1f}°)")
            print(f"3D gaze vector: [{gaze_3d[0,0]:.3f}, {gaze_3d[1,0]:.3f}, {gaze_3d[2,0]:.3f}]")
            
            # Head pose
            hr = face_result['head_pose']['rotation']
            ht = face_result['head_pose']['translation']
            print(f"Head rotation: [{hr[0,0]:.3f}, {hr[1,0]:.3f}, {hr[2,0]:.3f}] radians")
            print(f"Head translation: [{ht[0,0]:.3f}, {ht[1,0]:.3f}, {ht[2,0]:.3f}] mm")
            
            # Face location
            bbox = face_result['bbox']
            print(f"Face bounding box: ({bbox[0]}, {bbox[1]}) to ({bbox[2]}, {bbox[3]})")
            
            # Gaze projection points
            start_point = face_result['gaze_points']['start']
            end_point = face_result['gaze_points']['end']
            print(f"Gaze line: from {start_point} to {end_point}")


def example_real_time_processing():
    """Example showing how to process images from a webcam (simulated)."""
    print("\n=== Real-time Processing Example (Simulated) ===")
    
    detector = GazeDetector(
        model_cfg_path="configs/model/unigaze_h14.yaml",
        checkpoint_path="logs/unigaze_h14_joint.pth.tar",
        device="auto"
    )
    
    # Simulate processing frames from a video source
    # In practice, you would get frames from cv2.VideoCapture()
    
    print("Simulating real-time processing...")
    print("In a real application, you would:")
    print("1. Capture frames from webcam using cv2.VideoCapture()")
    print("2. Save each frame temporarily")
    print("3. Process with detector.process_image()")
    print("4. Display results in real-time")
    print("5. Optionally save interesting frames")
    
    # Example pseudo-code:
    """
    cap = cv2.VideoCapture(0)
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Save frame temporarily
        temp_path = "temp_frame.jpg"
        cv2.imwrite(temp_path, frame)
        
        # Process frame
        results = detector.process_image(temp_path)
        
        # Display results
        if results['faces_detected'] > 0:
            cv2.imshow('Gaze Detection', results['image_with_gaze'])
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
    """


if __name__ == "__main__":
    print("Gaze Detection Examples")
    print("=" * 50)
    
    # Run examples (comment out the ones you don't want to run)
    example_basic_usage()
    example_with_camera_parameters()
    example_batch_processing()
    example_detailed_analysis()
    example_real_time_processing()
    
    print("\nAll examples completed!")
    print("Note: Update the image paths and model paths before running.")
