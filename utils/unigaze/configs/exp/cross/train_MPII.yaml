

train: 
  - configs/data/mpiigaze.yaml

val: 
  
  - configs/data/eyediap_cs_train.yaml
  - configs/data/eyediap_cs_test.yaml
  - configs/data/eyediap_ft_train.yaml
  - configs/data/eyediap_ft_test.yaml
  

test: 
  - configs/data/xgaze_0_60sub.yaml
  - configs/data/xgaze_60_80sub.yaml
  - configs/data/gazecapture_train.yaml
  - configs/data/gazecapture_test.yaml
  - configs/data/gaze360_train.yaml
  - configs/data/gaze360_test.yaml


  - configs/data/eyediap_cs.yaml
  - configs/data/eyediap_ft.yaml
